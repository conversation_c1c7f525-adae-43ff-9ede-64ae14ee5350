#!/usr/bin/env python3
"""
Einfacher Starter für Michaels Screenshots
Verarbeitet Screenshots aus: C:\Users\<USER>\Documents\python\screenshots
"""

import os
import sys

# Versuche das Hauptmodul zu importieren
try:
    from screenshot_kpi_extractor import process_michaels_screenshots, CONFIG
    MAIN_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Hauptmodul nicht gefunden: {e}")
    print("💡 Verwende vereinfachte Version...")
    MAIN_MODULE_AVAILABLE = False

    # Fallback-Konfiguration
    CONFIG = {
        'default_screenshots_path': r'C:\Users\<USER>\Documents\python\screenshots'
    }

def main():
    print("🚀 Michael's Screenshot KPI Extractor")
    print("=" * 50)
    
    screenshots_path = CONFIG['default_screenshots_path']
    print(f"📁 Screenshots-Ordner: {screenshots_path}")
    
    # Prüfe ob Ordner existiert
    if not os.path.exists(screenshots_path):
        print(f"\n❌ Ordner nicht gefunden!")
        print(f"💡 Erstelle den Ordner: {screenshots_path}")
        print(f"📸 Lege deine PNG-Screenshots dort ab")
        
        # Ordner erstellen
        try:
            os.makedirs(screenshots_path, exist_ok=True)
            print(f"✅ Ordner erstellt: {screenshots_path}")
            print(f"📸 Lege jetzt deine Screenshots dort ab und führe das Script erneut aus")
        except Exception as e:
            print(f"❌ Fehler beim Erstellen des Ordners: {e}")
        
        return
    
    # Prüfe ob PNG-Dateien vorhanden
    png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]
    
    if not png_files:
        print(f"\n⚠️  Keine PNG-Dateien gefunden in: {screenshots_path}")
        print(f"📸 Lege deine Screenshots dort ab und führe das Script erneut aus")
        return
    
    print(f"📸 Gefunden: {len(png_files)} PNG-Dateien")
    print(f"📋 Beispiele: {png_files[:3]}")
    
    # Bestätigung
    print(f"\n🔄 Bereit zum Verarbeiten!")

    if MAIN_MODULE_AVAILABLE:
        response = input("Fortfahren? (j/n): ").lower().strip()

        if response in ['j', 'ja', 'y', 'yes', '']:
            print(f"\n🚀 Starte Verarbeitung...")
            success = process_michaels_screenshots()

            if success:
                print(f"\n🎉 Fertig! Öffne 'michael_kpi_data.xlsx' um die Ergebnisse zu sehen")
            else:
                print(f"\n💡 Tipps bei Problemen:")
                print(f"   - Prüfe ob die Screenshots scharf und gut lesbar sind")
                print(f"   - Stelle sicher, dass die KPI-Box sichtbar ist")
                print(f"   - Versuche es mit weniger Screenshots zum Testen")
        else:
            print(f"👋 Abgebrochen")
    else:
        print(f"\n❌ Hauptmodul nicht verfügbar")
        print(f"🔧 Setup-Schritte:")
        print(f"   1. Installiere Pakete: pip install pytesseract pillow pandas openpyxl requests")
        print(f"   2. Stelle sicher, dass screenshot_kpi_extractor.py im gleichen Ordner ist")
        print(f"   3. Führe direkt aus: python screenshot_kpi_extractor.py")
        print(f"\n📁 Aktuelles Verzeichnis: {os.getcwd()}")
        print(f"📄 Dateien hier: {[f for f in os.listdir('.') if f.endswith('.py')]}")

if __name__ == "__main__":
    main()
