#!/usr/bin/env python3
"""
Screenshot KPI Data Extractor
Extrahiert KPI-Daten aus Schichtplanungs-Screenshots und speichert sie in Excel/CSV

Benötigte Pakete:
pip install pytesseract pillow pandas openpyxl requests

Für Tesseract OCR:
- Windows: https://github.com/UB-Mannheim/tesseract/wiki
- Mac: brew install tesseract
- Linux: sudo apt-get install tesseract-ocr
"""

import os
import re
import pandas as pd
from datetime import datetime
from pathlib import Path
import argparse
import logging

# OCR Imports
try:
    import pytesseract
    from PIL import Image
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract nicht verfügbar. Installiere mit: pip install pytesseract pillow")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Requests nicht verfügbar. Installiere mit: pip install requests")

# Konfiguration
CONFIG = {
    'ocr_method': 'tesseract',  # 'tesseract' oder 'ocr_space'
    'ocr_space_api_key': 'helloworld',  # Demo API Key
    'tesseract_path': None,  # Automatisch erkennen oder Pfad setzen
    'output_format': 'excel',  # 'excel' oder 'csv'
    'output_file': 'kpi_data.xlsx',
    'log_level': 'INFO'
}

# Logging Setup
logging.basicConfig(
    level=getattr(logging, CONFIG['log_level']),
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ScreenshotKPIExtractor:
    def __init__(self, config=None):
        self.config = config or CONFIG
        self.setup_tesseract()
        
    def setup_tesseract(self):
        """Tesseract OCR konfigurieren"""
        if not TESSERACT_AVAILABLE:
            return
            
        if self.config['tesseract_path']:
            pytesseract.pytesseract.tesseract_cmd = self.config['tesseract_path']
        
        # Windows: Automatische Erkennung
        if os.name == 'nt':
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe'
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    break
    
    def extract_text_tesseract(self, image_path):
        """Text mit Tesseract OCR extrahieren"""
        if not TESSERACT_AVAILABLE:
            raise Exception("Tesseract nicht verfügbar")
        
        try:
            image = Image.open(image_path)
            # Tesseract Konfiguration für bessere Tabellenerkennung
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz%/()- '
            text = pytesseract.image_to_string(image, config=custom_config, lang='deu+eng')
            return text
        except Exception as e:
            logger.error(f"Tesseract Fehler bei {image_path}: {e}")
            return None
    
    def extract_text_ocr_space(self, image_path):
        """Text mit OCR.space API extrahieren"""
        if not REQUESTS_AVAILABLE:
            raise Exception("Requests nicht verfügbar")
        
        try:
            with open(image_path, 'rb') as image_file:
                response = requests.post(
                    'https://api.ocr.space/parse/image',
                    files={'file': image_file},
                    data={
                        'apikey': self.config['ocr_space_api_key'],
                        'language': 'ger',
                        'isOverlayRequired': False,
                        'detectOrientation': True,
                        'isTable': True,
                        'OCREngine': 2
                    },
                    timeout=30
                )
            
            result = response.json()
            if result.get('ParsedResults') and len(result['ParsedResults']) > 0:
                return result['ParsedResults'][0]['ParsedText']
            else:
                logger.warning(f"Kein Text erkannt in {image_path}")
                return None
                
        except Exception as e:
            logger.error(f"OCR.space Fehler bei {image_path}: {e}")
            return None
    
    def extract_text(self, image_path):
        """Text aus Bild extrahieren (je nach Konfiguration)"""
        if self.config['ocr_method'] == 'tesseract':
            return self.extract_text_tesseract(image_path)
        elif self.config['ocr_method'] == 'ocr_space':
            return self.extract_text_ocr_space(image_path)
        else:
            raise ValueError(f"Unbekannte OCR-Methode: {self.config['ocr_method']}")
    
    def parse_kpi_data(self, ocr_text, filename):
        """KPI-Daten aus OCR-Text extrahieren"""
        if not ocr_text:
            return None
        
        lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]
        
        # Initialisierung
        location = None
        planning_week = None
        expected_delta = None
        shifts_unable_to_assign = None
        trips_below_forecast = None
        runners_submitted_availabilities = None
        
        logger.debug(f"Verarbeite {filename}:")
        logger.debug(f"OCR Text (erste 300 Zeichen): {ocr_text[:300]}...")
        
        for i, line in enumerate(lines):
            line_lower = line.lower()
            
            # Location und Planungswoche
            if 'week planning' in line_lower:
                # Suche nach Wochennummer in dieser oder nächster Zeile
                week_match = re.search(r'(\d+)', line)
                if week_match:
                    planning_week = int(week_match.group(1))
                elif i + 1 < len(lines):
                    week_match = re.search(r'^(\d+)$', lines[i + 1])
                    if week_match:
                        planning_week = int(week_match.group(1))
                
                # Location in nächster Zeile suchen
                if i + 1 < len(lines):
                    location_match = re.search(r'^([A-Z]{2,4})$', lines[i + 1])
                    if location_match:
                        location = location_match.group(1)
            
            # Expected delta
            if 'expected delta' in line_lower or 'neg. hour balances' in line_lower:
                delta_value = self._find_number_in_context(lines, i, 3)
                if delta_value is not None:
                    expected_delta = delta_value
            
            # Shifts unable to assign
            if 'shifts unable' in line_lower or 'unable to be assigned' in line_lower:
                shifts_value = self._find_number_in_context(lines, i, 3)
                if shifts_value is not None:
                    shifts_unable_to_assign = shifts_value
            
            # Trips below forecast
            if 'trips below' in line_lower or 'below forecast' in line_lower:
                trips_value = self._find_number_in_context(lines, i, 3)
                if trips_value is not None:
                    trips_below_forecast = trips_value
            
            # Runners submitted availabilities
            if 'runners submitted' in line_lower or 'submitted availabilities' in line_lower:
                runners_value = self._find_number_in_context(lines, i, 3)
                if runners_value is not None:
                    runners_submitted_availabilities = runners_value
        
        # Ergebnis zusammenstellen
        result = {
            'filename': filename,
            'location': location,
            'planning_week': planning_week,
            'expected_delta': expected_delta,
            'shifts_unable_to_assign': shifts_unable_to_assign,
            'trips_below_forecast': trips_below_forecast,
            'runners_submitted_availabilities': runners_submitted_availabilities,
            'processed_date': datetime.now()
        }
        
        logger.info(f"Extrahierte Daten aus {filename}: {result}")
        
        # Nur zurückgeben wenn mindestens Location und Woche gefunden
        if location and planning_week:
            return result
        else:
            logger.warning(f"Unvollständige Daten in {filename}: Location={location}, Week={planning_week}")
            return None
    
    def _find_number_in_context(self, lines, start_index, max_lines):
        """Sucht nach einer Zahl in den nächsten N Zeilen"""
        for i in range(start_index, min(start_index + max_lines, len(lines))):
            line = lines[i]
            
            # Suche nach Zahlen (auch mit Prozent oder Vorzeichen)
            number_match = re.search(r'(-?\d+(?:\.\d+)?)\s*%?', line)
            if number_match:
                number = float(number_match.group(1))
                # Ignoriere sehr große Zahlen (wahrscheinlich Jahre)
                if abs(number) < 10000:
                    return number
        return None
    
    def process_directory(self, directory_path):
        """Verarbeitet alle PNG-Dateien in einem Verzeichnis"""
        directory = Path(directory_path)
        if not directory.exists():
            raise FileNotFoundError(f"Verzeichnis nicht gefunden: {directory_path}")
        
        png_files = list(directory.glob("*.png"))
        if not png_files:
            logger.warning(f"Keine PNG-Dateien in {directory_path} gefunden")
            return []
        
        logger.info(f"Gefunden: {len(png_files)} PNG-Dateien")
        
        all_data = []
        for png_file in png_files:
            logger.info(f"Verarbeite: {png_file.name}")
            
            try:
                ocr_text = self.extract_text(png_file)
                kpi_data = self.parse_kpi_data(ocr_text, png_file.name)
                
                if kpi_data:
                    all_data.append(kpi_data)
                    
            except Exception as e:
                logger.error(f"Fehler bei {png_file.name}: {e}")
        
        logger.info(f"Erfolgreich {len(all_data)} Dateien verarbeitet")
        return all_data
    
    def save_data(self, data, output_file=None):
        """Speichert die Daten in Excel oder CSV"""
        if not data:
            logger.warning("Keine Daten zum Speichern")
            return
        
        output_file = output_file or self.config['output_file']
        df = pd.DataFrame(data)
        
        # Spalten sortieren
        column_order = [
            'filename', 'location', 'planning_week', 'expected_delta',
            'shifts_unable_to_assign', 'trips_below_forecast', 
            'runners_submitted_availabilities', 'processed_date'
        ]
        df = df.reindex(columns=column_order)
        
        if output_file.endswith('.xlsx'):
            df.to_excel(output_file, index=False)
            logger.info(f"Daten gespeichert in: {output_file}")
        elif output_file.endswith('.csv'):
            df.to_csv(output_file, index=False)
            logger.info(f"Daten gespeichert in: {output_file}")
        else:
            raise ValueError("Ausgabedatei muss .xlsx oder .csv sein")

def main():
    parser = argparse.ArgumentParser(description='Screenshot KPI Data Extractor')
    parser.add_argument('directory', help='Verzeichnis mit PNG-Screenshots')
    parser.add_argument('-o', '--output', help='Ausgabedatei (Excel/CSV)', default='kpi_data.xlsx')
    parser.add_argument('--ocr', choices=['tesseract', 'ocr_space'], default='tesseract', help='OCR-Methode')
    parser.add_argument('--api-key', help='OCR.space API Key (falls verwendet)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    # Konfiguration anpassen
    config = CONFIG.copy()
    config['ocr_method'] = args.ocr
    config['output_file'] = args.output
    if args.api_key:
        config['ocr_space_api_key'] = args.api_key
    if args.verbose:
        config['log_level'] = 'DEBUG'
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Extractor erstellen und ausführen
    extractor = ScreenshotKPIExtractor(config)
    
    try:
        data = extractor.process_directory(args.directory)
        extractor.save_data(data, args.output)
        
        print(f"\n✅ Erfolgreich abgeschlossen!")
        print(f"📁 Verarbeitete Dateien: {len(data)}")
        print(f"💾 Ausgabedatei: {args.output}")
        
    except Exception as e:
        logger.error(f"Fehler: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
