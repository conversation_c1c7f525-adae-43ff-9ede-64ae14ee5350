# Setup-Anleitung: Screenshot Data Extractor

## 1. Google Apps Script Projekt erstellen

1. <PERSON><PERSON><PERSON> zu [script.google.com](https://script.google.com)
2. <PERSON><PERSON><PERSON> auf "Neues Projekt"
3. <PERSON><PERSON><PERSON> den Code aus `screenshot-data-extractor.gs` in den Editor
4. Speichere das Projekt mit einem Namen wie "Screenshot Data Extractor"

## 2. Google Cloud Vision API aktivieren

### Option A: Mit Google Cloud Platform (empfohlen)
1. <PERSON>eh<PERSON> zu [console.cloud.google.com](https://console.cloud.google.com)
2. Erstelle ein neues Projekt oder wähle ein bestehendes
3. Aktiviere die "Cloud Vision API"
4. In Apps Script: Gehe zu "Ressourcen" → "Cloud Platform-Projekt" 
5. Verknüpfe dein GCP-Projekt

### Option B: Mit API Key
1. In der Google Cloud Console: APIs & Services → Credentials
2. Erstelle einen API Key
3. Beschränke den Key auf die Vision API
4. Trage den Key in der CONFIG-Sektion ein

## 3. Konfiguration anpassen

Öffne das Apps Script und passe die CONFIG-Werte an:

```javascript
const CONFIG = {
  // 1. Google Drive Ordner ID finden:
  // - Öffne den Ordner mit den Screenshots in Google Drive
  // - Kopiere die ID aus der URL: drive.google.com/drive/folders/DIESE_ID_HIER
  DRIVE_FOLDER_ID: 'DEINE_ORDNER_ID_HIER',
  
  // 2. Google Sheet erstellen und ID kopieren:
  // - Erstelle ein neues Google Sheet
  // - Kopiere die ID aus der URL: docs.google.com/spreadsheets/d/DIESE_ID_HIER
  TARGET_SHEET_ID: 'DEINE_SHEET_ID_HIER',
  
  // 3. Name des Arbeitsblatts (wird automatisch erstellt)
  SHEET_NAME: 'Extracted_Data',
  
  // 4. Nur bei Option B (API Key) ausfüllen
  VISION_API_KEY: '' // Leer lassen wenn GCP Projekt verwendet wird
};
```

## 4. Berechtigungen erteilen

1. Führe die Funktion `processAllScreenshots()` einmal aus
2. Erlaube alle angeforderten Berechtigungen:
   - Google Drive Zugriff
   - Google Sheets Zugriff
   - Externe Verbindungen (für Vision API)

## 5. Testen

1. Führe zuerst `testSingleFile()` aus:
   - Ersetze `DEINE_TEST_DATEI_ID` mit der ID einer Test-PNG-Datei
   - Prüfe die Logs auf Fehler
   
2. Wenn der Test erfolgreich ist, führe `processAllScreenshots()` aus

## 6. Automatisierung (optional)

Für regelmäßige Ausführung:
1. Gehe zu "Trigger" im Apps Script Editor
2. Erstelle einen neuen Trigger für `processAllScreenshots`
3. Wähle Zeitintervall (z.B. täglich, wöchentlich)

## Erwartete Ausgabe im Google Sheet

Das Script erstellt folgende Spalten:
- **File Name**: Name der Screenshot-Datei
- **Week**: Kalenderwoche
- **Year**: Jahr
- **Employee ID**: Mitarbeiter-ID (M1, S1, etc.)
- **Date**: Datum (Format: Tag/Monat)
- **Day of Week**: Wochentag
- **Planned Shifts**: Geplante Schichten
- **Target Shifts**: Ziel-Schichten
- **Raw Shift Data**: Rohdaten der Schicht
- **Processed Date**: Verarbeitungsdatum

## Troubleshooting

### Häufige Probleme:

1. **"Keine Berechtigung"**
   - Stelle sicher, dass alle Berechtigungen erteilt wurden
   - Prüfe, ob die Drive/Sheets IDs korrekt sind

2. **"Vision API Fehler"**
   - Prüfe, ob die Vision API aktiviert ist
   - Bei API Key: Prüfe Gültigkeit und Beschränkungen
   - Bei GCP: Prüfe Projektverknüpfung

3. **"Keine Daten extrahiert"**
   - Prüfe die Bildqualität der Screenshots
   - Teste mit `testSingleFile()` und prüfe OCR-Output
   - Möglicherweise muss die `parseScheduleData()` Funktion angepasst werden

4. **"Falsche Daten erkannt"**
   - Die OCR kann bei schlechter Bildqualität Fehler machen
   - Passe die RegEx-Patterns in `parseScheduleData()` an
   - Prüfe die Logs für OCR-Rohdaten

### Debug-Tipps:

- Nutze `console.log()` um OCR-Rohdaten zu prüfen
- Teste mit einzelnen, klaren Screenshots
- Prüfe die Logs im Apps Script Editor unter "Ausführungen"

## Kosten

- Google Cloud Vision API: Erste 1000 Anfragen/Monat kostenlos
- Danach: ~$1.50 pro 1000 Bilder
- Apps Script: Kostenlos (mit Limits)
