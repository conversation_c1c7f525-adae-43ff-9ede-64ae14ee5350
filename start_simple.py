#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Einfacher Screenshot-Verarbeiter für Michael
<PERSON>oji<PERSON>, nur Text - funktioniert auf allen Windows-Systemen
"""

import os
import sys
import requests
import re
import csv
from datetime import datetime

def main():
    print("=" * 60)
    print("Michael's Screenshot KPI Extractor - Einfache Version")
    print("=" * 60)
    
    # Screenshots-Pfad
    screenshots_path = r'C:\Users\<USER>\Documents\python\screenshots'
    
    print(f"Screenshots-Ordner: {screenshots_path}")
    
    # Prüfe ob Ordner existiert
    if not os.path.exists(screenshots_path):
        print(f"FEHLER: Screenshots-Ordner nicht gefunden!")
        print(f"Erstelle den Ordner und lege PNG-Dateien hinein.")
        
        try:
            os.makedirs(screenshots_path, exist_ok=True)
            print(f"Ordner wurde erstellt: {screenshots_path}")
        except Exception as e:
            print(f"<PERSON><PERSON> be<PERSON>: {e}")
            return
    
    # Suche PNG-<PERSON>ien
    png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]
    
    if not png_files:
        print(f"FEHLER: Keine PNG-Dateien gefunden!")
        print(f"Lege Screenshots in: {screenshots_path}")
        return
    
    print(f"Gefundene PNG-Dateien: {len(png_files)}")
    for i, file in enumerate(png_files[:5], 1):
        print(f"  {i}. {file}")
    
    if len(png_files) > 5:
        print(f"  ... und {len(png_files) - 5} weitere")
    
    # Bestätigung
    print(f"\nBereit zum Verarbeiten!")
    response = input("Fortfahren? (j/n): ").lower().strip()
    
    if response not in ['j', 'ja', 'y', 'yes', '']:
        print("Abgebrochen")
        return
    
    # Verarbeitung starten
    print(f"\nStarte Verarbeitung mit OCR.space...")
    results = []
    
    # Verarbeite erste 3 Dateien (Demo)
    for i, png_file in enumerate(png_files[:3], 1):
        file_path = os.path.join(screenshots_path, png_file)
        print(f"\n[{i}/3] Verarbeite: {png_file}")
        
        try:
            # OCR.space API aufrufen
            with open(file_path, 'rb') as image_file:
                response = requests.post(
                    'https://api.ocr.space/parse/image',
                    files={'file': image_file},
                    data={
                        'apikey': 'helloworld',
                        'language': 'ger',
                        'isTable': True,
                        'OCREngine': 2
                    },
                    timeout=30
                )
            
            result = response.json()
            
            if result.get('ParsedResults'):
                ocr_text = result['ParsedResults'][0]['ParsedText']
                print(f"  OCR erfolgreich ({len(ocr_text)} Zeichen)")
                
                # Daten extrahieren
                data = extract_kpi_data(ocr_text, png_file)
                if data:
                    results.append(data)
                    print(f"  Daten extrahiert: {data['location']} Week {data['planning_week']}")
                else:
                    print(f"  Keine KPI-Daten gefunden")
            else:
                print(f"  OCR fehlgeschlagen")
                
        except Exception as e:
            print(f"  FEHLER: {e}")
    
    # Ergebnisse speichern
    if results:
        output_file = save_results(results)
        print(f"\nERFOLG!")
        print(f"Ergebnisse gespeichert in: {output_file}")
        print(f"Verarbeitete Dateien: {len(results)}")
        
        # Ergebnisse anzeigen
        print(f"\nExtrahierte Daten:")
        for result in results:
            print(f"  {result['filename']}: {result['location']} Week {result['planning_week']} Delta {result['expected_delta']}")
    else:
        print(f"\nKeine Daten extrahiert.")
        print(f"Tipps:")
        print(f"  - Stelle sicher, dass die Screenshots scharf sind")
        print(f"  - Die KPI-Box sollte gut sichtbar sein")
        print(f"  - Text sollte klar lesbar sein")

def extract_kpi_data(ocr_text, filename):
    """Extrahiert KPI-Daten aus OCR-Text"""
    lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]
    
    location = None
    planning_week = None
    expected_delta = None
    
    # Debug: Zeige ersten Teil des Textes
    print(f"  Text-Vorschau: {ocr_text[:100]}...")
    
    for i, line in enumerate(lines):
        line_lower = line.lower()
        
        # Suche nach "Week Planning" und Woche
        if 'week' in line_lower and 'planning' in line_lower:
            week_match = re.search(r'(\d+)', line)
            if week_match:
                planning_week = int(week_match.group(1))
                print(f"    Gefunden: Planning Week {planning_week}")
            
            # Suche Location in nächsten Zeilen
            for j in range(i + 1, min(i + 4, len(lines))):
                location_match = re.search(r'^([A-Z]{2,4})$', lines[j].strip())
                if location_match:
                    location = location_match.group(1)
                    print(f"    Gefunden: Location {location}")
                    break
        
        # Suche nach "Expected Delta"
        if 'expected' in line_lower and 'delta' in line_lower:
            # Suche Zahl in dieser und nächsten Zeilen
            for j in range(i, min(i + 3, len(lines))):
                delta_match = re.search(r'(-?\d+)', lines[j])
                if delta_match:
                    expected_delta = int(delta_match.group(1))
                    print(f"    Gefunden: Expected Delta {expected_delta}")
                    break
    
    # Rückgabe nur wenn mindestens Location und Week gefunden
    if location and planning_week:
        return {
            'filename': filename,
            'location': location,
            'planning_week': planning_week,
            'expected_delta': expected_delta or 0
        }
    
    return None

def save_results(results):
    """Speichert Ergebnisse in CSV-Datei"""
    output_file = 'michael_kpi_results.csv'
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['filename', 'location', 'planning_week', 'expected_delta', 'processed_date']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            result['processed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            writer.writerow(result)
    
    return output_file

if __name__ == "__main__":
    main()
