/**
 * Google Apps Script für das Extrahieren von Schichtplanungsdaten aus Screenshots
 * Verwendet Google Cloud Vision API für OCR
 */

// Konfiguration - Diese Werte musst du anpassen
const CONFIG = {
  // Google Drive Ordner ID mit den Screenshots
  DRIVE_FOLDER_ID: 'DEINE_ORDNER_ID_HIER',
  
  // Google Sheet ID wo die Daten gespeichert werden sollen
  TARGET_SHEET_ID: 'DEINE_SHEET_ID_HIER',
  
  // Name des Arbeitsblatts
  SHEET_NAME: 'Extracted_Data',
  
  // Google Cloud Vision API Key (optional, falls nicht über GCP Projekt verfügbar)
  VISION_API_KEY: 'DEIN_API_KEY_HIER' // Leer lassen wenn GCP Projekt verwendet wird
};

/**
 * Hauptfunktion - Verarbeitet alle Screenshots im angegebenen Ordner
 */
function processAllScreenshots() {
  try {
    console.log('Starte Verarbeitung der Screenshots...');
    
    // Zugriff auf den Drive Ordner
    const folder = DriveApp.getFolderById(CONFIG.DRIVE_FOLDER_ID);
    const files = folder.getFilesByType(MimeType.PNG);
    
    // Ziel-Sheet vorbereiten
    const targetSheet = prepareTargetSheet();
    
    let processedCount = 0;
    const allExtractedData = [];
    
    // Alle PNG-Dateien verarbeiten
    while (files.hasNext()) {
      const file = files.next();
      console.log(`Verarbeite: ${file.getName()}`);
      
      try {
        const extractedData = processScreenshot(file);
        if (extractedData && extractedData.length > 0) {
          allExtractedData.push(...extractedData);
          processedCount++;
        }
      } catch (error) {
        console.error(`Fehler bei ${file.getName()}: ${error.message}`);
      }
    }
    
    // Daten in Sheet schreiben
    if (allExtractedData.length > 0) {
      writeDataToSheet(targetSheet, allExtractedData);
      console.log(`Erfolgreich ${processedCount} Screenshots verarbeitet und ${allExtractedData.length} Datensätze extrahiert.`);
    } else {
      console.log('Keine Daten extrahiert.');
    }
    
  } catch (error) {
    console.error(`Hauptfehler: ${error.message}`);
    throw error;
  }
}

/**
 * Verarbeitet einen einzelnen Screenshot
 */
function processScreenshot(file) {
  const imageBlob = file.getBlob();
  const ocrText = performOCR(imageBlob);
  
  if (!ocrText) {
    console.log(`Kein Text in ${file.getName()} erkannt`);
    return [];
  }
  
  return parseScheduleData(ocrText, file.getName());
}

/**
 * Führt OCR mit Google Cloud Vision API durch
 */
function performOCR(imageBlob) {
  try {
    const base64Image = Utilities.base64Encode(imageBlob.getBytes());
    
    const requestBody = {
      requests: [{
        image: {
          content: base64Image
        },
        features: [{
          type: 'TEXT_DETECTION',
          maxResults: 1
        }]
      }]
    };
    
    let response;
    if (CONFIG.VISION_API_KEY) {
      // Mit API Key
      const url = `https://vision.googleapis.com/v1/images:annotate?key=${CONFIG.VISION_API_KEY}`;
      response = UrlFetchApp.fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        payload: JSON.stringify(requestBody)
      });
    } else {
      // Mit OAuth (GCP Projekt)
      const url = 'https://vision.googleapis.com/v1/images:annotate';
      response = UrlFetchApp.fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${ScriptApp.getOAuthToken()}`,
          'Content-Type': 'application/json'
        },
        payload: JSON.stringify(requestBody)
      });
    }
    
    const result = JSON.parse(response.getContentText());
    
    if (result.responses && result.responses[0] && result.responses[0].textAnnotations) {
      return result.responses[0].textAnnotations[0].description;
    }
    
    return null;
    
  } catch (error) {
    console.error(`OCR Fehler: ${error.message}`);
    return null;
  }
}

/**
 * Parst die OCR-Textdaten und extrahiert Schichtplanungsinformationen
 */
function parseScheduleData(ocrText, fileName) {
  const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  const extractedData = [];
  
  let currentWeek = null;
  let currentYear = null;
  let weekDates = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Woche und Jahr erkennen
    const weekMatch = line.match(/Week\s+(\d+)/i);
    if (weekMatch) {
      currentWeek = parseInt(weekMatch[1]);
      continue;
    }
    
    const yearMatch = line.match(/(\d{4})/);
    if (yearMatch && !currentYear) {
      currentYear = parseInt(yearMatch[1]);
      continue;
    }
    
    // Datumszeile erkennen (Mon 28/4, Tue 29/4, etc.)
    if (line.includes('Mon') && line.includes('Tue')) {
      weekDates = parseDateLine(line);
      continue;
    }
    
    // Mitarbeiterdaten erkennen (M1, M2, S1, etc.)
    const employeeMatch = line.match(/^(M\d+|S\d+|E\d+|EM\d+|G6M\d+|G6A\d+)\s+(.+)/);
    if (employeeMatch && currentWeek && currentYear) {
      const employeeId = employeeMatch[1];
      const shiftData = employeeMatch[2];
      
      const shifts = parseShiftData(shiftData);
      
      // Für jeden Tag einen Datensatz erstellen
      shifts.forEach((shift, dayIndex) => {
        if (shift && dayIndex < weekDates.length) {
          extractedData.push({
            fileName: fileName,
            week: currentWeek,
            year: currentYear,
            employeeId: employeeId,
            date: weekDates[dayIndex],
            dayOfWeek: getDayName(dayIndex),
            plannedShifts: shift.planned || '',
            targetShifts: shift.target || '',
            rawShiftData: shift.raw || ''
          });
        }
      });
    }
  }
  
  return extractedData;
}

/**
 * Parst eine Datumszeile und extrahiert die Wochentage
 */
function parseDateLine(line) {
  const datePattern = /(\w+)\s+(\d+\/\d+)/g;
  const dates = [];
  let match;
  
  while ((match = datePattern.exec(line)) !== null) {
    dates.push(match[2]); // z.B. "28/4"
  }
  
  return dates;
}

/**
 * Parst Schichtdaten aus einer Zeile
 */
function parseShiftData(shiftLine) {
  // Suche nach Mustern wie "8 / 8", "6 / 6", "10 / 10"
  const shiftPattern = /(\d+)\s*\/\s*(\d+)/g;
  const shifts = [];
  let match;
  
  while ((match = shiftPattern.exec(shiftLine)) !== null) {
    shifts.push({
      planned: match[1],
      target: match[2],
      raw: match[0]
    });
  }
  
  // Falls keine Muster gefunden, versuche einzelne Zahlen
  if (shifts.length === 0) {
    const numbers = shiftLine.match(/\d+/g);
    if (numbers) {
      numbers.forEach(num => {
        shifts.push({
          planned: num,
          target: '',
          raw: num
        });
      });
    }
  }
  
  return shifts;
}

/**
 * Gibt den Wochentag-Namen zurück
 */
function getDayName(dayIndex) {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  return days[dayIndex] || '';
}

/**
 * Bereitet das Ziel-Sheet vor
 */
function prepareTargetSheet() {
  const spreadsheet = SpreadsheetApp.openById(CONFIG.TARGET_SHEET_ID);
  
  let sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
  if (!sheet) {
    sheet = spreadsheet.insertSheet(CONFIG.SHEET_NAME);
  }
  
  // Header setzen
  const headers = [
    'File Name',
    'Week',
    'Year', 
    'Employee ID',
    'Date',
    'Day of Week',
    'Planned Shifts',
    'Target Shifts',
    'Raw Shift Data',
    'Processed Date'
  ];
  
  if (sheet.getLastRow() === 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
  }
  
  return sheet;
}

/**
 * Schreibt die extrahierten Daten in das Sheet
 */
function writeDataToSheet(sheet, data) {
  if (data.length === 0) return;
  
  const startRow = sheet.getLastRow() + 1;
  const rows = data.map(item => [
    item.fileName,
    item.week,
    item.year,
    item.employeeId,
    item.date,
    item.dayOfWeek,
    item.plannedShifts,
    item.targetShifts,
    item.rawShiftData,
    new Date()
  ]);
  
  sheet.getRange(startRow, 1, rows.length, rows[0].length).setValues(rows);
  
  console.log(`${rows.length} Zeilen in Sheet geschrieben`);
}

/**
 * Test-Funktion für einzelne Datei
 */
function testSingleFile() {
  // Ersetze mit einer spezifischen Datei-ID zum Testen
  const fileId = 'DEINE_TEST_DATEI_ID';
  const file = DriveApp.getFileById(fileId);
  
  const extractedData = processScreenshot(file);
  console.log('Extrahierte Daten:', extractedData);
}
