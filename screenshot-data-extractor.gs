/**
 * Google Apps Script für das Extrahieren von KPI-Daten aus Schichtplanungs-Screenshots
 * Verwendet Google Cloud Vision API für OCR
 *
 * Extrahiert folgende Daten:
 * - Location (3-Zeichen Kürzel)
 * - Planungswoche
 * - Expected delta in neg. hour balances
 * - Shifts unable to be assigned
 * - Trips below forecast
 * - Runners submitted availabilities
 */

// Konfiguration - Diese Werte musst du anpassen
const CONFIG = {
  // Google Drive Ordner ID mit den Screenshots
  DRIVE_FOLDER_ID: '1DMuxRmYdZE0yJGV1G42e8bobLImwF0zL',

  // Google Sheet ID wo die Daten gespeichert werden sollen
  TARGET_SHEET_ID: '1i5gLZZ5v83ImFCuPL-EXg2MLi90xx7RIolcFIawXb-Y',

  // Name des Arbeitsblatts
  SHEET_NAME: 'DE',

  // Google Cloud Vision API Key (optional, falls nicht über GCP Projekt verfügbar)
  VISION_API_KEY: '' // Leer lassen wenn GCP Projekt verwendet wird
};

/**
 * Hauptfunktion - Verarbeitet alle Screenshots im angegebenen Ordner
 */
function processAllScreenshots() {
  try {
    console.log('Starte Verarbeitung der Screenshots...');
    
    // Zugriff auf den Drive Ordner
    const folder = DriveApp.getFolderById(CONFIG.DRIVE_FOLDER_ID);
    const files = folder.getFilesByType(MimeType.PNG);
    
    // Ziel-Sheet vorbereiten
    const targetSheet = prepareTargetSheet();
    
    let processedCount = 0;
    const allExtractedData = [];
    
    // Alle PNG-Dateien verarbeiten
    while (files.hasNext()) {
      const file = files.next();
      console.log(`Verarbeite: ${file.getName()}`);
      
      try {
        const extractedData = processScreenshot(file);
        if (extractedData && extractedData.length > 0) {
          allExtractedData.push(...extractedData);
          processedCount++;
        }
      } catch (error) {
        console.error(`Fehler bei ${file.getName()}: ${error.message}`);
      }
    }
    
    // Daten in Sheet schreiben
    if (allExtractedData.length > 0) {
      writeDataToSheet(targetSheet, allExtractedData);
      console.log(`Erfolgreich ${processedCount} Screenshots verarbeitet und ${allExtractedData.length} Datensätze extrahiert.`);
    } else {
      console.log('Keine Daten extrahiert.');
    }
    
  } catch (error) {
    console.error(`Hauptfehler: ${error.message}`);
    throw error;
  }
}

/**
 * Verarbeitet einen einzelnen Screenshot
 */
function processScreenshot(file) {
  const imageBlob = file.getBlob();
  const ocrText = performOCR(imageBlob);
  
  if (!ocrText) {
    console.log(`Kein Text in ${file.getName()} erkannt`);
    return [];
  }
  
  return parseScheduleData(ocrText, file.getName());
}

/**
 * Führt OCR mit OCR.space API durch (Alternative zu Google Vision)
 */
function performOCR(imageBlob) {
  try {
    // Option 1: OCR.space API (kostenlos, einfach)
    return performOCRWithOCRSpace(imageBlob);

    // Option 2: Google Vision API (auskommentiert)
    // return performOCRWithGoogleVision(imageBlob);

  } catch (error) {
    console.error(`OCR Fehler: ${error.message}`);
    return null;
  }
}

/**
 * OCR mit OCR.space API (kostenlos, 25.000 Anfragen/Monat)
 */
function performOCRWithOCRSpace(imageBlob) {
  const apiKey = 'helloworld'; // Kostenloser API Key für Tests
  // Für Produktion: Registriere dich auf ocr.space für eigenen API Key

  const formData = {
    'apikey': apiKey,
    'language': 'ger', // Deutsch
    'isOverlayRequired': false,
    'detectOrientation': true,
    'isTable': true, // Optimiert für Tabellen
    'OCREngine': 2 // Engine 2 ist besser für Tabellen
  };

  const response = UrlFetchApp.fetch('https://api.ocr.space/parse/image', {
    method: 'POST',
    payload: {
      ...formData,
      'file': imageBlob
    }
  });

  const result = JSON.parse(response.getContentText());

  if (result.ParsedResults && result.ParsedResults.length > 0) {
    return result.ParsedResults[0].ParsedText;
  }

  return null;
}

/**
 * OCR mit Google Cloud Vision API (Original-Implementierung)
 */
function performOCRWithGoogleVision(imageBlob) {
  const base64Image = Utilities.base64Encode(imageBlob.getBytes());

  const requestBody = {
    requests: [{
      image: {
        content: base64Image
      },
      features: [{
        type: 'TEXT_DETECTION',
        maxResults: 1
      }]
    }]
  };

  let response;
  if (CONFIG.VISION_API_KEY) {
    // Mit API Key
    const url = `https://vision.googleapis.com/v1/images:annotate?key=${CONFIG.VISION_API_KEY}`;
    response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestBody)
    });
  } else {
    // Mit OAuth (GCP Projekt)
    const url = 'https://vision.googleapis.com/v1/images:annotate';
    response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ScriptApp.getOAuthToken()}`,
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestBody)
    });
  }

  const result = JSON.parse(response.getContentText());

  if (result.responses && result.responses[0] && result.responses[0].textAnnotations) {
    return result.responses[0].textAnnotations[0].description;
  }

  return null;
}

/**
 * Parst die OCR-Textdaten und extrahiert KPI-Informationen
 */
function parseScheduleData(ocrText, fileName) {
  const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  let location = null;
  let planningWeek = null;
  let expectedDelta = null;
  let shiftsUnableToAssign = null;
  let tripsBelowForecast = null;
  let runnersSubmittedAvailabilities = null;

  console.log(`Verarbeite ${fileName}:`);
  console.log('OCR Text:', ocrText.substring(0, 500) + '...');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const nextLine = i + 1 < lines.length ? lines[i + 1] : '';

    // Location und Planungswoche aus "Week Planning" Zeile
    const weekPlanningMatch = line.match(/Week\s+Planning\s*(\d+)?/i);
    if (weekPlanningMatch) {
      // Planungswoche direkt in der Zeile oder in der nächsten Zeile
      if (weekPlanningMatch[1]) {
        planningWeek = parseInt(weekPlanningMatch[1]);
      } else {
        const weekNumberMatch = nextLine.match(/^(\d+)$/);
        if (weekNumberMatch) {
          planningWeek = parseInt(weekNumberMatch[1]);
        }
      }

      // Location ist normalerweise in der Zeile darunter (3-Zeichen Kürzel)
      const locationMatch = nextLine.match(/^([A-Z]{2,4})$/);
      if (locationMatch) {
        location = locationMatch[1];
      }
      continue;
    }

    // Alternative: Location direkt unter "Week Planning"
    if (line.toLowerCase().includes('week planning') && !location) {
      const locationMatch = nextLine.match(/^([A-Z]{2,4})$/);
      if (locationMatch) {
        location = locationMatch[1];
      }
    }

    // Expected delta in neg. hour balances
    if (line.toLowerCase().includes('expected delta') ||
        line.toLowerCase().includes('neg. hour balances')) {
      const deltaMatch = findNumberInNextLines(lines, i, 3);
      if (deltaMatch !== null) {
        expectedDelta = deltaMatch;
      }
    }

    // Shifts unable to be assigned
    if (line.toLowerCase().includes('shifts unable') ||
        line.toLowerCase().includes('unable to be assigned')) {
      const shiftsMatch = findNumberInNextLines(lines, i, 3);
      if (shiftsMatch !== null) {
        shiftsUnableToAssign = shiftsMatch;
      }
    }

    // Trips below forecast
    if (line.toLowerCase().includes('trips below') ||
        line.toLowerCase().includes('below forecast')) {
      const tripsMatch = findNumberInNextLines(lines, i, 3);
      if (tripsMatch !== null) {
        tripsBelowForecast = tripsMatch;
      }
    }

    // Runners submitted availabilities
    if (line.toLowerCase().includes('runners submitted') ||
        line.toLowerCase().includes('submitted availabilities')) {
      const runnersMatch = findNumberInNextLines(lines, i, 3);
      if (runnersMatch !== null) {
        runnersSubmittedAvailabilities = runnersMatch;
      }
    }
  }

  // Ergebnis zusammenstellen
  const result = {
    fileName: fileName,
    location: location,
    planningWeek: planningWeek,
    expectedDelta: expectedDelta,
    shiftsUnableToAssign: shiftsUnableToAssign,
    tripsBelowForecast: tripsBelowForecast,
    runnersSubmittedAvailabilities: runnersSubmittedAvailabilities,
    processedDate: new Date()
  };

  console.log('Extrahierte Daten:', result);

  // Nur zurückgeben wenn mindestens Location und Woche gefunden wurden
  if (location && planningWeek) {
    return [result];
  } else {
    console.log(`Unvollständige Daten in ${fileName}: Location=${location}, Week=${planningWeek}`);
    return [];
  }
}

/**
 * Hilfsfunktion: Sucht nach einer Zahl in den nächsten N Zeilen
 */
function findNumberInNextLines(lines, startIndex, maxLines) {
  for (let i = startIndex; i < Math.min(startIndex + maxLines, lines.length); i++) {
    const line = lines[i];

    // Suche nach Zahlen (auch mit Prozentzeichen oder Dezimalstellen)
    const numberMatch = line.match(/(\d+(?:\.\d+)?)\s*%?/);
    if (numberMatch) {
      const number = parseFloat(numberMatch[1]);
      // Ignoriere sehr große Zahlen (wahrscheinlich Jahre oder IDs)
      if (number < 10000) {
        return number;
      }
    }
  }
  return null;
}

/**
 * Bereitet das Ziel-Sheet vor
 */
function prepareTargetSheet() {
  const spreadsheet = SpreadsheetApp.openById(CONFIG.TARGET_SHEET_ID);

  let sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
  if (!sheet) {
    sheet = spreadsheet.insertSheet(CONFIG.SHEET_NAME);
  }

  // Header setzen
  const headers = [
    'File Name',
    'Location',
    'Planning Week',
    'Expected Delta',
    'Shifts Unable to Assign',
    'Trips Below Forecast',
    'Runners Submitted Availabilities',
    'Processed Date'
  ];

  if (sheet.getLastRow() === 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
  }

  return sheet;
}

/**
 * Schreibt die extrahierten Daten in das Sheet
 */
function writeDataToSheet(sheet, data) {
  if (data.length === 0) return;

  const startRow = sheet.getLastRow() + 1;
  const rows = data.map(item => [
    item.fileName,
    item.location,
    item.planningWeek,
    item.expectedDelta,
    item.shiftsUnableToAssign,
    item.tripsBelowForecast,
    item.runnersSubmittedAvailabilities,
    item.processedDate
  ]);

  sheet.getRange(startRow, 1, rows.length, rows[0].length).setValues(rows);

  console.log(`${rows.length} Zeilen in Sheet geschrieben`);
}

/**
 * Test-Funktion für einzelne Datei
 */
function testSingleFile() {
  // Ersetze mit einer spezifischen Datei-ID zum Testen
  const fileId = 'DEINE_TEST_DATEI_ID';
  const file = DriveApp.getFileById(fileId);
  
  const extractedData = processScreenshot(file);
  console.log('Extrahierte Daten:', extractedData);
}
